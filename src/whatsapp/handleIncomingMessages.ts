import makeWASocket, { type MessageUpsertType, type WAMessage, isJidBroadcast, isJidStatusBroadcast } from "baileys";
import { mastra } from "../mastra/index.js";
import { RuntimeContext } from "@mastra/core/di";
import type { ChatbotRuntimeContext } from "../mastra/agents/chatbot-agent.js";
import { formatMessageDateTime } from "../utils/messageParser.js";
import { getMessageText, getSenderName, shouldBotRespond } from "./helpers.js";

export async function handleIncomingMessages(
    { messages, type }: { messages: WAMessage[]; type: MessageUpsertType },
    sock: ReturnType<typeof makeWASocket>
) {
    if (type !== "notify") return;

    for (const msg of messages) {
        if (!msg.message) continue;
        if (msg.key.fromMe) continue; // Ignore messages sent by the bot
        if (isJidBroadcast(msg.key.remoteJid!) || isJidStatusBroadcast(msg.key.remoteJid!)) continue;

        const messageText = getMessageText(msg);
        if (!messageText) continue;

        // Check if the bot should respond to this message (mentioned or replied to)
        if (!shouldBotRespond(msg, sock)) continue;

        const userJid = msg.key.participant || msg.key.remoteJid!;

        // Extract user ID and chat ID for proper memory management
        const userFormattedId = userJid.replace("@s.whatsapp.net", "").replace("@g.us", ""); // User who sent the message
        const chatJid = msg.key.remoteJid!; // Chat where message was sent

        console.log("Received message:", messageText, "from:", userFormattedId);

        // Extract sender information
        const senderName = await getSenderName(msg, sock);

        // Here you can integrate with your AI agent
        const response = await generateBotResponse(messageText, userJid, chatJid, msg, senderName);

        if (response) {
            await sock.sendMessage(msg.key.remoteJid!, { text: response });
        }
    }
}


async function generateBotResponse(
    messageText: string,
    userJid: string,
    chatJid: string,
    message: WAMessage,
    senderName: string
): Promise<string | null> {
    try {
        const agent = mastra.getAgent("chatbotAgent");
        const runtimeContext = new RuntimeContext<ChatbotRuntimeContext>();
        runtimeContext.set("config_model", "gemini-2.5-flash");
        runtimeContext.set("config_botName", "Jeff");

        // Include current user information with the question and timestamp
        const timestamp = message.messageTimestamp ? Number(message.messageTimestamp) : Date.now() / 1000;
        const currentMessageDateTime = formatMessageDateTime(timestamp);
        const formattedMessage = `${senderName} [${currentMessageDateTime}]:\n${messageText}`;
        console.log("formattedMessage:", formattedMessage);

        const response = await agent.generate(formattedMessage, {
            resourceId: userJid, // Use user's WhatsApp ID for memory persistence
            threadId: chatJid, // Use chat ID as thread ID for conversation context
            runtimeContext,
        });

        return response.text || null;
    } catch (error) {
        console.error("Error generating bot response:", error);
        return null;
    }
}
