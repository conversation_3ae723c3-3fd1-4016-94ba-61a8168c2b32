import makeWASocket, { type MessageUpsertType, type WAMessage, isJidBroadcast, isJidStatusBroadcast } from "baileys";
import { mastra } from "../mastra/index.js";
import { RuntimeContext } from "@mastra/core/di";
import type { ChatbotRuntimeContext } from "../mastra/agents/chatbot-agent.js";
import { formatMessageDateTime } from "../utils/messageParser.js";
import { saveImageToFile, getImageInfo } from "../utils/imageUtils.js";
import {
    getMessageText,
    getSenderName,
    shouldBotRespond,
    hasImageMessage,
    downloadImage,
    getImageMimeType,
} from "./helpers.js";

export async function handleIncomingMessages(
    { messages, type }: { messages: WAMessage[]; type: MessageUpsertType },
    sock: ReturnType<typeof makeWASocket>
) {
    if (type !== "notify") return;

    for (const msg of messages) {
        if (!msg.message) continue;
        if (msg.key.fromMe) continue; // Ignore messages sent by the bot
        if (isJidBroadcast(msg.key.remoteJid!) || isJidStatusBroadcast(msg.key.remoteJid!)) continue;

        const messageText = getMessageText(msg);
        const hasImage = hasImageMessage(msg);

        // Skip if no text and no image
        if (!messageText && !hasImage) continue;

        // Check if the bot should respond to this message (mentioned or replied to)
        if (!shouldBotRespond(msg, sock)) continue;

        const userJid = msg.key.participant || msg.key.remoteJid!;

        // Extract user ID and chat ID for proper memory management
        const userFormattedId = userJid.replace("@s.whatsapp.net", "").replace("@g.us", ""); // User who sent the message
        const chatJid = msg.key.remoteJid!; // Chat where message was sent

        console.log(
            "Received message:",
            messageText || "[Image]",
            "from:",
            userFormattedId,
            hasImage ? "with image" : ""
        );

        // Extract sender information
        const senderName = await getSenderName(msg, sock);

        // Download image if present
        let imageBuffer: Buffer | null = null;
        let imageMimeType: string | null = null;
        let imagePath: string | null = null;
        if (hasImage) {
            imageBuffer = await downloadImage(msg);
            imageMimeType = getImageMimeType(msg);

            if (imageBuffer && imageMimeType) {
                // Save image to file for future processing
                imagePath = saveImageToFile(imageBuffer, imageMimeType, userJid);
                const imageInfo = getImageInfo(imageBuffer, imageMimeType);
                console.log("Downloaded and saved image:", imageInfo, "to:", imagePath);
            } else {
                console.log("Failed to download image");
            }
        }

        // Here you can integrate with your AI agent
        const response = await generateBotResponse(
            messageText,
            userJid,
            chatJid,
            msg,
            senderName,
            imageBuffer,
            imageMimeType
        );

        if (response) {
            await sock.sendMessage(msg.key.remoteJid!, { text: response });
        }
    }
}

async function generateBotResponse(
    messageText: string | null,
    userJid: string,
    chatJid: string,
    message: WAMessage,
    senderName: string,
    imageBuffer?: Buffer | null,
    imageMimeType?: string | null
): Promise<string | null> {
    try {
        const agent = mastra.getAgent("chatbotAgent");
        const runtimeContext = new RuntimeContext<ChatbotRuntimeContext>();
        runtimeContext.set("config_model", "gemini-2.5-flash");
        runtimeContext.set("config_botName", "Jeff");

        // Include current user information with the question and timestamp
        const timestamp = message.messageTimestamp ? Number(message.messageTimestamp) : Date.now() / 1000;
        const currentMessageDateTime = formatMessageDateTime(timestamp);

        // Format message with image information if present
        let formattedMessage = `${senderName} [${currentMessageDateTime}]:`;

        if (messageText) {
            formattedMessage += `\n${messageText}`;
        }

        if (imageBuffer && imageMimeType) {
            if (messageText) {
                formattedMessage += `\n[User also sent an image with this caption]`;
            } else {
                formattedMessage += `\n[User sent an image]`;
            }
        }

        console.log(
            "formattedMessage:",
            formattedMessage.substring(0, 200) + (formattedMessage.length > 200 ? "..." : "")
        );

        // Prepare messages for the agent - handle images with vision models
        let messages: any[];

        if (imageBuffer && imageMimeType) {
            // Use the AI SDK format for image attachments
            messages = [
                {
                    role: "user",
                    content: formattedMessage,
                    experimental_attachments: [
                        {
                            name: "image",
                            contentType: imageMimeType,
                            data: imageBuffer,
                        },
                    ],
                },
            ];
        } else {
            // Text-only content
            messages = [
                {
                    role: "user",
                    content: formattedMessage,
                },
            ];
        }

        const response = await agent.generate(messages, {
            resourceId: userJid, // Use user's WhatsApp ID for memory persistence
            threadId: chatJid, // Use chat ID as thread ID for conversation context
            runtimeContext,
        });

        return response.text || null;
    } catch (error) {
        console.error("Error generating bot response:", error);
        return null;
    }
}
