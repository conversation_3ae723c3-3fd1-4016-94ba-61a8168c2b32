import makeWASocket, { type ConnectionState, DisconnectReason, useMultiFileAuthState } from "baileys";
import { Boom } from "@hapi/boom";
import QRCode from "qrcode";
import { existsSync, mkdirSync } from "fs";
import { join } from "path";
import { handleIncomingMessages } from "./handleIncomingMessages";
import pino from "pino";

interface WhatsAppService {
    sock: ReturnType<typeof makeWASocket> | null;
    qrCode: string | null;
    connectionState: ConnectionState;
    isConnected: boolean;
}

const AUTH_DIR = join(process.cwd(), "auth_info_baileys");

// Ensure auth directory exists
if (!existsSync(AUTH_DIR)) {
    mkdirSync(AUTH_DIR, { recursive: true });
}

export async function createWhatsAppService(): Promise<WhatsAppService> {
    const service: WhatsAppService = {
        sock: null,
        qrCode: null,
        connectionState: { connection: "close" },
        isConnected: false,
    };

    const { state, saveCreds } = await useMultiFileAuthState(AUTH_DIR);

    // Create a custom logger that suppresses verbose session logs
    const logger = pino({
        level: "warn", // Only show warnings and errors, suppress info/debug logs
        transport: {
            target: "pino-pretty",
            options: {
                colorize: true,
                ignore: "pid,hostname",
                translateTime: "SYS:standard",
            },
        },
    });

    const sock = makeWASocket({
        auth: state,
        printQRInTerminal: false,
        browser: ["Personal AI Bot", "Chrome", "1.0.0"],
        logger: logger,
    });

    service.sock = sock;

    sock.ev.on("connection.update", async (update) => {
        const { connection, lastDisconnect, qr } = update;
        if (connection !== undefined) {
            service.connectionState = {
                connection,
                lastDisconnect: lastDisconnect || { error: undefined, date: new Date() },
            };
        }

        if (qr) {
            try {
                service.qrCode = await QRCode.toDataURL(qr);
                console.log("QR Code generated");
            } catch (err) {
                console.error("Error generating QR code:", err);
            }
        }

        if (connection === "close") {
            service.isConnected = false;
            const shouldReconnect = (lastDisconnect?.error as Boom)?.output?.statusCode !== DisconnectReason.loggedOut;
            console.log("Connection closed due to:", lastDisconnect?.error, ", reconnecting:", shouldReconnect);

            if (shouldReconnect) {
                setTimeout(() => createWhatsAppService(), 3000);
            }
        } else if (connection === "open") {
            service.isConnected = true;
            service.qrCode = null;
            console.log("WhatsApp connection opened");
        }
    });

    sock.ev.on("creds.update", saveCreds);

    sock.ev.on("messages.upsert", async (m) => {
        await handleIncomingMessages(m, sock);
    });

    return service;
}

let whatsappService: WhatsAppService | null = null;

export async function getWhatsAppService(): Promise<WhatsAppService> {
    if (!whatsappService) {
        whatsappService = await createWhatsAppService();
    }
    return whatsappService;
}

export function getQRCode(): string | null {
    return whatsappService?.qrCode || null;
}

export function getConnectionState(): ConnectionState {
    return whatsappService?.connectionState || { connection: "close" };
}

export function isWhatsAppConnected(): boolean {
    return whatsappService?.isConnected || false;
}
