import makeWASocket, { type WAMessage, downloadMediaMessage } from "baileys";

export function getMessageText(msg: WAMessage): string | null {
    if (msg.message?.conversation) {
        return msg.message.conversation;
    }
    if (msg.message?.extendedTextMessage?.text) {
        return msg.message.extendedTextMessage.text;
    }
    // Handle image messages with captions
    if (msg.message?.imageMessage?.caption) {
        return msg.message.imageMessage.caption;
    }
    return null;
}

export function hasImageMessage(msg: WAMessage): boolean {
    return !!msg.message?.imageMessage;
}

export async function downloadImage(msg: WAMessage): Promise<Buffer | null> {
    try {
        if (!msg.message?.imageMessage) {
            return null;
        }

        const buffer = await downloadMediaMessage(
            msg,
            "buffer",
            {},
            {
                logger: console as any,
                reuploadRequest: (msg: any) => Promise.resolve(msg),
            }
        );

        return buffer as Buffer;
    } catch (error) {
        console.error("Error downloading image:", error);
        return null;
    }
}

export function getImageMimeType(msg: WAMessage): string | null {
    return msg.message?.imageMessage?.mimetype || null;
}

export async function getSenderName(msg: WAMessage, _sock: ReturnType<typeof makeWASocket>): Promise<string> {
    try {
        const senderJid = msg.key.participant || msg.key.remoteJid!;

        if (msg.pushName) {
            return msg.pushName;
        }

        return senderJid.replace("@s.whatsapp.net", "").replace("@g.us", "");
    } catch (error) {
        console.warn("Error getting sender name:", error);
        const senderJid = msg.key.participant || msg.key.remoteJid!;
        return senderJid.replace("@s.whatsapp.net", "").replace("@g.us", "");
    }
}

export function shouldBotRespond(msg: WAMessage, sock: ReturnType<typeof makeWASocket>): boolean {
    const botJid = sock.user?.id;
    if (!botJid) {
        console.log("No bot JID available");
        return false;
    }

    if (process.env.NODE_ENV === "development") {
        console.log("Bot user info:", JSON.stringify(sock.user, null, 2));
    }

    if (!msg.key.remoteJid?.endsWith("@g.us")) {
        console.log("Direct message, responding");
        return true;
    }

    const isMentioned = isBotMentioned(msg, botJid, sock.user);
    const isReply = isReplyToBotMessage(msg, botJid);

    console.log(`Group message - Bot mentioned: ${isMentioned}, Is reply: ${isReply}, Bot JID: ${botJid}`);

    return isMentioned || isReply;
}

export function isBotMentioned(msg: WAMessage, botJid: string, botUser?: any): boolean {
    let mentions: string[] = [];

    if (msg.message?.extendedTextMessage?.contextInfo?.mentionedJid) {
        mentions = mentions.concat(msg.message.extendedTextMessage.contextInfo.mentionedJid);
        console.log(
            `Found mentions in extendedTextMessage: ${msg.message.extendedTextMessage.contextInfo.mentionedJid.join(", ")}`
        );
    }

    // Check for mentions in image messages
    if (msg.message?.imageMessage?.contextInfo?.mentionedJid) {
        mentions = mentions.concat(msg.message.imageMessage.contextInfo.mentionedJid);
        console.log(`Found mentions in imageMessage: ${msg.message.imageMessage.contextInfo.mentionedJid.join(", ")}`);
    }

    console.log(`Checking mentions: [${mentions.join(", ")}] against bot JID: ${botJid}`);

    const exactMatch = mentions.includes(botJid);
    const normalizedBotJid = normalizeBotJid(botJid);

    const normalizedMatch = mentions.some((mention) => {
        const normalizedMention = normalizeQuotedParticipant(mention);
        console.log(`Comparing normalized mention: ${normalizedMention} with bot: ${normalizedBotJid}`);
        return normalizedMention === normalizedBotJid;
    });

    let lidMatch = false;
    if (botUser?.lid) {
        const baseLid = botUser.lid.split(":")[0];
        lidMatch = mentions.includes(`${baseLid}@lid`);
        console.log(`LID check - Bot LID: ${botUser.lid}, Base LID: ${baseLid}, LID match: ${lidMatch}`);
    }

    console.log(`Mention check - Exact: ${exactMatch}, Normalized: ${normalizedMatch}, LID: ${lidMatch}`);
    return exactMatch || normalizedMatch || lidMatch;
}

export function isReplyToBotMessage(msg: WAMessage, botJid: string): boolean {
    let quotedMessage = null;
    let quotedParticipant = null;

    if (msg.message?.extendedTextMessage?.contextInfo) {
        quotedMessage = msg.message.extendedTextMessage.contextInfo.quotedMessage;
        quotedParticipant = msg.message.extendedTextMessage.contextInfo.participant;
    }

    // Check for replies in image messages
    if (msg.message?.imageMessage?.contextInfo) {
        quotedMessage = msg.message.imageMessage.contextInfo.quotedMessage;
        quotedParticipant = msg.message.imageMessage.contextInfo.participant;
    }

    console.log(
        `Reply check - Quoted message exists: ${!!quotedMessage}, Quoted participant: ${quotedParticipant}, Bot JID: ${botJid}`
    );

    if (!quotedMessage || !quotedParticipant) {
        console.log("No quoted message or participant found");
        return false;
    }

    const normalizedBotJid = normalizeBotJid(botJid);
    const normalizedQuotedParticipant = normalizeQuotedParticipant(quotedParticipant);

    console.log(`Normalized comparison - Bot: ${normalizedBotJid}, Quoted: ${normalizedQuotedParticipant}`);

    const exactMatch = quotedParticipant === botJid;
    console.log(`Exact JID match: ${exactMatch}`);

    const isFromBot = normalizedQuotedParticipant === normalizedBotJid || exactMatch;
    console.log(`Is reply from bot: ${isFromBot}`);

    return isFromBot;
}

export function normalizeBotJid(botJid: string): string {
    const parts = botJid.split(":");
    return parts[0]?.split("@")[0] || botJid;
}

export function normalizeQuotedParticipant(participantJid: string): string {
    if (participantJid.includes("@lid")) {
        const parts = participantJid.split("@");
        return parts[0] || participantJid;
    }
    const parts = participantJid.split(":");
    const firstPart = parts[0] || participantJid;
    const finalParts = firstPart.split("@");
    return finalParts[0] || firstPart;
}
