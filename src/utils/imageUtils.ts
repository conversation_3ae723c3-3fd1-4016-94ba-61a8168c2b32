import { existsSync, mkdirSync, writeFileSync } from "fs";
import { join } from "path";
import { createHash } from "crypto";

const IMAGES_DIR = join(process.cwd(), "received_images");

// Ensure images directory exists
if (!existsSync(IMAGES_DIR)) {
    mkdirSync(IMAGES_DIR, { recursive: true });
}

export function saveImageToFile(imageBuffer: Buffer, mimeType: string, userJid: string): string | null {
    try {
        // Generate a unique filename based on content hash and timestamp
        const hash = createHash('md5').update(imageBuffer).digest('hex');
        const timestamp = Date.now();
        const extension = getFileExtension(mimeType);
        const userSafe = userJid.replace(/[@.]/g, '_');
        
        const filename = `${userSafe}_${timestamp}_${hash.substring(0, 8)}.${extension}`;
        const filepath = join(IMAGES_DIR, filename);
        
        writeFileSync(filepath, imageBuffer);
        console.log(`Image saved to: ${filepath}`);
        
        return filepath;
    } catch (error) {
        console.error("Error saving image:", error);
        return null;
    }
}

function getFileExtension(mimeType: string): string {
    const mimeToExt: Record<string, string> = {
        'image/jpeg': 'jpg',
        'image/jpg': 'jpg',
        'image/png': 'png',
        'image/gif': 'gif',
        'image/webp': 'webp',
        'image/bmp': 'bmp',
        'image/tiff': 'tiff',
    };
    
    return mimeToExt[mimeType.toLowerCase()] || 'bin';
}

export function getImageInfo(imageBuffer: Buffer, mimeType: string): string {
    const sizeKB = Math.round(imageBuffer.length / 1024);
    return `${mimeType} image (${sizeKB} KB)`;
}
