# WhatsApp Image Handling

This document describes the image handling capabilities added to the WhatsApp AI bot using Bailey<PERSON>.

## Features

### Image Reception
- **Automatic Detection**: The bot automatically detects when a WhatsApp message contains an image
- **Caption Support**: Images with captions are properly handled, with both the image and caption text processed
- **Multiple Formats**: Supports common image formats (JPEG, PNG, GIF, WebP, BMP, TIFF)

### Image Processing
- **Download**: Images are automatically downloaded from WhatsApp servers
- **Local Storage**: Images are saved locally in the `received_images/` directory with unique filenames
- **Vision AI**: Images are passed to the Gemini 2.5 Flash vision model for analysis and response generation

### Bot Response Behavior
- **Direct Messages**: <PERSON><PERSON> responds to all images in direct messages
- **Group Messages**: <PERSON><PERSON> responds to images when:
  - The bot is mentioned in the image caption
  - The image is sent as a reply to the bot's message
  - The image message contains mentions in the context

## Technical Implementation

### File Structure
```
src/
├── whatsapp/
│   ├── handleIncomingMessages.ts  # Main message handler with image support
│   └── helpers.ts                 # Image detection and download utilities
└── utils/
    └── imageUtils.ts              # Image saving and metadata utilities
```

### Key Functions

#### `hasImageMessage(msg: WAMessage): boolean`
Checks if a WhatsApp message contains an image.

#### `downloadImage(msg: WAMessage): Promise<Buffer | null>`
Downloads the image from WhatsApp servers and returns it as a Buffer.

#### `getImageMimeType(msg: WAMessage): string | null`
Extracts the MIME type of the image from the message.

#### `saveImageToFile(imageBuffer: Buffer, mimeType: string, userJid: string): string | null`
Saves the image to the local filesystem with a unique filename.

### Vision Model Integration

Images are passed to the Mastra agent using the multimodal content format:

```typescript
const content = [
    {
        type: "text",
        text: formattedMessage
    },
    {
        type: "image", 
        image: `data:${imageMimeType};base64,${base64Image}`
    }
];

const response = await agent.generate(content, options);
```

### File Naming Convention

Images are saved with the following naming pattern:
```
{userJid}_{timestamp}_{hash}.{extension}
```

Where:
- `userJid`: Sanitized WhatsApp user ID
- `timestamp`: Unix timestamp when image was received
- `hash`: First 8 characters of MD5 hash of image content
- `extension`: File extension based on MIME type

## Usage Examples

### Sending Images to the Bot

1. **Direct Message with Image**: Send any image to the bot in a direct message
2. **Group Message with Mention**: Send an image in a group and mention the bot
3. **Image with Caption**: Send an image with a text caption for context
4. **Reply with Image**: Reply to one of the bot's messages with an image

### Bot Responses

The bot can:
- Describe what it sees in the image
- Answer questions about the image content
- Provide context-aware responses based on both image and caption
- Remember image content for future conversations (via memory system)

## Configuration

### Model Requirements
- The bot uses Google's Gemini 2.5 Flash model which supports vision capabilities
- No additional configuration needed for basic image processing

### Storage
- Images are stored in `received_images/` directory in the project root
- Directory is automatically created if it doesn't exist
- No automatic cleanup - images persist until manually removed

## Error Handling

- Failed image downloads are logged but don't crash the bot
- Unsupported image formats fall back to text-only processing
- Network errors during download are gracefully handled

## Future Enhancements

- [ ] Automatic image cleanup after processing
- [ ] Image compression for large files
- [ ] Support for other media types (videos, documents)
- [ ] Image analysis caching to avoid reprocessing
- [ ] Custom vision model integration options
